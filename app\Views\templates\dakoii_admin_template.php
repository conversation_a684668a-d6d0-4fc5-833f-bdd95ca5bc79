<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= isset($title) ? esc($title) : 'Dakoii Panel' ?></title>

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="<?= base_url('assets/system_images/favicon.ico') ?>">
    <link rel="shortcut icon" type="image/x-icon" href="<?= base_url('assets/system_images/favicon.ico') ?>">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #6366f1;
            --secondary-color: #8b5cf6;
            --dark-bg: #0f172a;
            --dark-card: #1e293b;
            --dark-border: #334155;
            --text-light: #f1f5f9;
            --text-muted: #94a3b8;
        }

        body {
            background: var(--dark-bg);
            color: var(--text-light);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .navbar-dark {
            background: var(--dark-card) !important;
            border-bottom: 1px solid var(--dark-border);
        }

        .card-dark {
            background: var(--dark-card);
            border: 1px solid var(--dark-border);
            color: var(--text-light);
        }

        .btn-primary-custom {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            border: none;
            color: white;
        }

        .btn-primary-custom:hover {
            background: linear-gradient(135deg, #5855eb 0%, #7c3aed 100%);
            color: white;
        }

        .text-primary-custom {
            color: var(--primary-color) !important;
        }

        .sidebar {
            background: var(--dark-card);
            border-right: 1px solid var(--dark-border);
            min-height: calc(100vh - 56px);
        }

        .sidebar .nav-link {
            color: var(--text-muted);
            padding: 0.75rem 1rem;
            border-radius: 0.375rem;
            margin: 0.25rem 0;
        }

        .sidebar .nav-link:hover {
            background: rgba(99, 102, 241, 0.1);
            color: var(--primary-color);
        }

        .sidebar .nav-link.active {
            background: var(--primary-color);
            color: white;
        }

        .main-content {
            background: var(--dark-bg);
            min-height: calc(100vh - 56px);
        }

        .dropdown-menu-dark {
            background: var(--dark-card);
            border: 1px solid var(--dark-border);
        }

        .dropdown-item {
            color: var(--text-light);
        }

        .dropdown-item:hover {
            background: rgba(99, 102, 241, 0.1);
            color: var(--primary-color);
        }

        /* Ensure text visibility */
        .card-dark .card-body {
            color: var(--text-light) !important;
        }

        .card-dark h1, .card-dark h2, .card-dark h3,
        .card-dark h4, .card-dark h5, .card-dark h6 {
            color: var(--text-light) !important;
        }

        .card-dark p, .card-dark span, .card-dark div {
            color: var(--text-light);
        }

        .card-dark .text-muted {
            color: var(--text-muted) !important;
        }

        .btn-outline-primary:hover, .btn-outline-success:hover,
        .btn-outline-warning:hover, .btn-outline-info:hover {
            color: white !important;
        }

        .list-group-item {
            color: var(--text-light) !important;
        }

        .list-group-item h6 {
            color: var(--text-light) !important;
        }

        .list-group-item p {
            color: var(--text-muted) !important;
        }

        /* Sidebar submenu styling */
        .sidebar .nav-link {
            transition: all 0.3s ease;
            border-radius: 8px;
            margin-bottom: 2px;
        }

        .sidebar .nav-link:hover {
            background: rgba(99, 102, 241, 0.1);
            color: var(--primary-color);
        }

        .sidebar .nav-link.active {
            background: var(--primary-color) !important;
            color: white !important;
            font-weight: 600;
            box-shadow: 0 2px 4px rgba(99, 102, 241, 0.3);
        }

        .sidebar .nav-link.active i {
            color: white !important;
        }

        .sidebar .collapse .nav-link {
            font-size: 0.9rem;
            padding: 0.5rem 1rem;
        }

        .sidebar .bi-chevron-down {
            transition: transform 0.3s ease;
        }

        .sidebar .nav-link[aria-expanded="true"] .bi-chevron-down {
            transform: rotate(180deg);
        }

        /* Footer styling */
        .footer {
            margin-top: auto;
        }

        body {
            display: flex;
            flex-direction: column;
            min-height: 100vh;
        }

        .container-fluid {
            flex: 1;
        }
    </style>
</head>

<?php
// Get current URI segments for active menu highlighting
$uri = service('uri');
$segment1 = $uri->getSegment(1); // 'dakoii'
$segment2 = $uri->getSegment(2); // 'dashboard', 'users', 'organizations', etc.
$segment3 = $uri->getSegment(3); // 'new', 'edit', etc.
$currentPath = '/' . $segment1 . '/' . $segment2;
?>

<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark fixed-top">
        <div class="container-fluid">
            <a class="navbar-brand fw-bold text-primary-custom d-flex align-items-center" href="<?= base_url('dakoii/dashboard') ?>">
                <img src="<?= base_url('assets/system_images/dakoii-logo-icon.png') ?>" alt="Dakoii" style="width: 32px; height: 32px;" class="me-2">
                Dakoii Panel
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-person-circle me-2"></i><?= session()->get('dakoii_name') ?>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-dark dropdown-menu-end">
                            <li><a class="dropdown-item" href="#"><i class="bi bi-person me-2"></i>Profile</a></li>
                            <li><a class="dropdown-item" href="#"><i class="bi bi-gear me-2"></i>Settings</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="<?= base_url('dakoii/logout') ?>"><i class="bi bi-box-arrow-right me-2"></i>Logout</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container-fluid" style="margin-top: 56px;">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 px-0">
                <div class="sidebar p-3">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link <?= ($segment2 == 'dashboard') ? 'active' : '' ?>" href="<?= base_url('dakoii/dashboard') ?>">
                                <i class="bi bi-speedometer2 me-2"></i>Dashboard
                            </a>
                        </li>

                        <!-- Organization Management -->
                        <li class="nav-item">
                            <a class="nav-link <?= ($segment2 == 'organizations') ? 'active' : '' ?>" href="#" data-bs-toggle="collapse" data-bs-target="#orgSubmenu" aria-expanded="<?= ($segment2 == 'organizations') ? 'true' : 'false' ?>">
                                <i class="bi bi-building me-2"></i>Organizations
                                <i class="bi bi-chevron-down ms-auto"></i>
                            </a>
                            <div class="collapse <?= ($segment2 == 'organizations') ? 'show' : '' ?>" id="orgSubmenu">
                                <ul class="nav flex-column ms-3">
                                    <li class="nav-item">
                                        <a class="nav-link <?= ($segment2 == 'organizations' && ($segment3 == '' || $segment3 == null)) ? 'active' : '' ?>" href="<?= base_url('dakoii/organizations') ?>">
                                            <i class="bi bi-list me-2"></i>View Organizations
                                        </a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link <?= ($segment2 == 'organizations' && $segment3 == 'new') ? 'active' : '' ?>" href="<?= base_url('dakoii/organizations/new') ?>">
                                            <i class="bi bi-plus-circle me-2"></i>Add Organization
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </li>

                        <!-- User Management -->
                        <li class="nav-item">
                            <a class="nav-link <?= ($segment2 == 'system-users') ? 'active' : '' ?>" href="#" data-bs-toggle="collapse" data-bs-target="#userSubmenu" aria-expanded="<?= ($segment2 == 'system-users') ? 'true' : 'false' ?>">
                                <i class="bi bi-people me-2"></i>User Management
                                <i class="bi bi-chevron-down ms-auto"></i>
                            </a>
                            <div class="collapse <?= ($segment2 == 'system-users') ? 'show' : '' ?>" id="userSubmenu">
                                <ul class="nav flex-column ms-3">
                                    <li class="nav-item">
                                        <a class="nav-link <?= ($segment2 == 'system-users' && ($segment3 == '' || $segment3 == null)) ? 'active' : '' ?>" href="<?= base_url('dakoii/system-users') ?>">
                                            <i class="bi bi-list me-2"></i>View Users
                                        </a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link <?= ($segment2 == 'system-users' && $segment3 == 'new') ? 'active' : '' ?>" href="<?= base_url('dakoii/system-users/new') ?>">
                                            <i class="bi bi-person-plus me-2"></i>Add User
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </li>

                        <!-- Geographic Data -->
                        <li class="nav-item">
                            <a class="nav-link" href="#" data-bs-toggle="collapse" data-bs-target="#geoSubmenu" aria-expanded="false">
                                <i class="bi bi-globe me-2"></i>Geographic Data
                                <i class="bi bi-chevron-down ms-auto"></i>
                            </a>
                            <div class="collapse" id="geoSubmenu">
                                <ul class="nav flex-column ms-3">
                                    <li class="nav-item">
                                        <a class="nav-link" href="#">
                                            <i class="bi bi-flag me-2"></i>Countries
                                        </a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link" href="#">
                                            <i class="bi bi-map me-2"></i>Provinces/States
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </li>

                        <!-- Admin Management -->
                        <li class="nav-item">
                            <a class="nav-link <?= ($segment2 == 'users') ? 'active' : '' ?>" href="#" data-bs-toggle="collapse" data-bs-target="#adminSubmenu" aria-expanded="<?= ($segment2 == 'users') ? 'true' : 'false' ?>">
                                <i class="bi bi-shield-check me-2"></i>Admin Management
                                <i class="bi bi-chevron-down ms-auto"></i>
                            </a>
                            <div class="collapse <?= ($segment2 == 'users') ? 'show' : '' ?>" id="adminSubmenu">
                                <ul class="nav flex-column ms-3">
                                    <li class="nav-item">
                                        <a class="nav-link <?= ($segment2 == 'users') ? 'active' : '' ?>" href="<?= base_url('dakoii/users') ?>">
                                            <i class="bi bi-person-gear me-2"></i>Admin Users
                                        </a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link" href="#">
                                            <i class="bi bi-key me-2"></i>Permissions
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </li>

                        <!-- Reports & Analytics -->
                        <li class="nav-item">
                            <a class="nav-link" href="#">
                                <i class="bi bi-graph-up me-2"></i>Analytics
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#">
                                <i class="bi bi-file-earmark-text me-2"></i>Reports
                            </a>
                        </li>

                        <!-- System -->
                        <li class="nav-item">
                            <a class="nav-link" href="#">
                                <i class="bi bi-gear me-2"></i>System Settings
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#">
                                <i class="bi bi-question-circle me-2"></i>Help & Support
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
            
            <!-- Main Content -->
            <div class="col-md-9 col-lg-10">
                <div class="main-content">
                    <!-- Flash Messages -->
                    <?php if (session()->getFlashdata('success')): ?>
                        <div class="alert alert-success alert-dismissible fade show m-3" role="alert">
                            <i class="bi bi-check-circle me-2"></i>
                            <?= session()->getFlashdata('success') ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>
                    
                    <?php if (session()->getFlashdata('error')): ?>
                        <div class="alert alert-danger alert-dismissible fade show m-3" role="alert">
                            <i class="bi bi-exclamation-triangle me-2"></i>
                            <?= session()->getFlashdata('error') ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>
                    
                    <?php if (session()->getFlashdata('warning')): ?>
                        <div class="alert alert-warning alert-dismissible fade show m-3" role="alert">
                            <i class="bi bi-exclamation-triangle me-2"></i>
                            <?= session()->getFlashdata('warning') ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>
                    
                    <?php if (session()->getFlashdata('info')): ?>
                        <div class="alert alert-info alert-dismissible fade show m-3" role="alert">
                            <i class="bi bi-info-circle me-2"></i>
                            <?= session()->getFlashdata('info') ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>
                    
                    <!-- Page Content -->
                    <?= $this->renderSection('content') ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="footer mt-5 py-4" style="background: var(--card-bg); border-top: 1px solid var(--border-color);">
        <div class="container-fluid">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <div class="d-flex align-items-center">
                        <img src="<?= base_url('assets/system_images/dakoii-systems-logo.png') ?>" alt="Dakoii Systems" style="height: 40px;" class="me-3">
                        <div>
                            <p class="mb-0 text-light"><strong>Dakoii Systems</strong></p>
                            <small class="text-light opacity-75">Professional Collection Management Platform</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 text-md-end">
                    <div class="mb-2">
                        <small class="text-light opacity-75">
                            Version 1.0.0 |
                            <a href="#" class="text-primary text-decoration-none">Documentation</a> |
                            <a href="#" class="text-primary text-decoration-none">Support</a>
                        </small>
                    </div>
                    <small class="text-light opacity-75">
                        © <?= date('Y') ?> Dakoii Systems. All rights reserved.
                    </small>
                </div>
            </div>
            <div class="row mt-3">
                <div class="col-12">
                    <div class="d-flex justify-content-center">
                        <small class="text-light opacity-75 text-center">
                            Powered by <img src="<?= base_url('assets/system_images/pcollx_logo.png') ?>" alt="PCollX" style="height: 16px;" class="mx-1"> PCollX Framework
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Custom Scripts -->
    <script>
        // Auto-collapse sidebar submenus when clicking outside
        document.addEventListener('click', function(e) {
            if (!e.target.closest('.sidebar')) {
                const collapses = document.querySelectorAll('.sidebar .collapse.show');
                collapses.forEach(collapse => {
                    const bsCollapse = new bootstrap.Collapse(collapse, {toggle: false});
                    bsCollapse.hide();
                });
            }
        });


    </script>

    <?= $this->renderSection('scripts') ?>
</body>
</html>
