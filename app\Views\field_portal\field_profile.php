<?= $this->extend('templates/field_template') ?>

<?= $this->section('content') ?>

<!-- Page Header -->
<div class="card">
    <h1 class="card-title">👤 My Profile</h1>
    <p style="color: #666; margin-bottom: 0;">Manage your profile and settings</p>
</div>

<!-- Profile Information -->
<div class="card">
    <h2 class="card-title">📋 Profile Information</h2>
    
    <form id="profileForm" method="post" action="<?= base_url('field/profile/update') ?>">
        <!-- Name -->
        <div class="form-group">
            <label class="form-label" for="name">👤 Full Name</label>
            <input type="text" 
                   id="name" 
                   name="name" 
                   class="form-control" 
                   value="<?= esc($user['name']) ?>" 
                   required>
        </div>

        <!-- Email -->
        <div class="form-group">
            <label class="form-label" for="email">📧 Email Address</label>
            <input type="email" 
                   id="email" 
                   name="email" 
                   class="form-control" 
                   value="<?= esc($user['email']) ?>" 
                   required>
        </div>

        <!-- Phone -->
        <div class="form-group">
            <label class="form-label" for="phone">📱 Phone Number</label>
            <input type="tel" 
                   id="phone" 
                   name="phone" 
                   class="form-control" 
                   value="+************" 
                   placeholder="+675 XXX XXXX">
        </div>

        <!-- Role (Read-only) -->
        <div class="form-group">
            <label class="form-label">🏷️ Role</label>
            <div style="background: #f8f9fa; padding: 12px; border-radius: 6px; color: #666;">
                <?= esc($user['role']) ?> (Field Officer)
            </div>
        </div>

        <!-- Location -->
        <div class="form-group">
            <label class="form-label" for="location">📍 Primary Location</label>
            <input type="text" 
                   id="location" 
                   name="location" 
                   class="form-control" 
                   value="Wewak, East Sepik Province" 
                   placeholder="Your primary work location">
        </div>

        <!-- Update Button -->
        <button type="submit" class="btn btn-success btn-block">
            💾 Update Profile
        </button>
    </form>
</div>

<!-- Change Password -->
<div class="card">
    <h2 class="card-title">🔒 Change Password</h2>
    
    <form id="passwordForm" method="post" action="<?= base_url('field/profile/change-password') ?>">
        <!-- Current Password -->
        <div class="form-group">
            <label class="form-label" for="current_password">🔑 Current Password</label>
            <input type="password" 
                   id="current_password" 
                   name="current_password" 
                   class="form-control" 
                   required>
        </div>

        <!-- New Password -->
        <div class="form-group">
            <label class="form-label" for="new_password">🆕 New Password</label>
            <input type="password" 
                   id="new_password" 
                   name="new_password" 
                   class="form-control" 
                   minlength="6"
                   required>
        </div>

        <!-- Confirm Password -->
        <div class="form-group">
            <label class="form-label" for="confirm_password">✅ Confirm New Password</label>
            <input type="password" 
                   id="confirm_password" 
                   name="confirm_password" 
                   class="form-control" 
                   minlength="6"
                   required>
        </div>

        <!-- Change Password Button -->
        <button type="submit" class="btn btn-warning btn-block">
            🔒 Change Password
        </button>
    </form>
</div>

<!-- App Settings -->
<div class="card">
    <h2 class="card-title">⚙️ App Settings</h2>
    
    <!-- Offline Mode -->
    <div style="display: flex; justify-content: space-between; align-items: center; padding: 15px 0; border-bottom: 1px solid #eee;">
        <div>
            <strong>📱 Offline Mode</strong>
            <div style="font-size: 14px; color: #666;">Save data locally when offline</div>
        </div>
        <label style="position: relative; display: inline-block; width: 50px; height: 24px;">
            <input type="checkbox" id="offline_mode" checked style="opacity: 0; width: 0; height: 0;">
            <span style="position: absolute; cursor: pointer; top: 0; left: 0; right: 0; bottom: 0; background-color: #28a745; transition: .4s; border-radius: 24px;"></span>
            <span style="position: absolute; content: ''; height: 18px; width: 18px; left: 3px; bottom: 3px; background-color: white; transition: .4s; border-radius: 50%; transform: translateX(26px);"></span>
        </label>
    </div>

    <!-- Auto-sync -->
    <div style="display: flex; justify-content: space-between; align-items: center; padding: 15px 0; border-bottom: 1px solid #eee;">
        <div>
            <strong>🔄 Auto-sync</strong>
            <div style="font-size: 14px; color: #666;">Automatically sync when online</div>
        </div>
        <label style="position: relative; display: inline-block; width: 50px; height: 24px;">
            <input type="checkbox" id="auto_sync" checked style="opacity: 0; width: 0; height: 0;">
            <span style="position: absolute; cursor: pointer; top: 0; left: 0; right: 0; bottom: 0; background-color: #28a745; transition: .4s; border-radius: 24px;"></span>
            <span style="position: absolute; content: ''; height: 18px; width: 18px; left: 3px; bottom: 3px; background-color: white; transition: .4s; border-radius: 50%; transform: translateX(26px);"></span>
        </label>
    </div>

    <!-- Photo Quality -->
    <div style="display: flex; justify-content: space-between; align-items: center; padding: 15px 0; border-bottom: 1px solid #eee;">
        <div>
            <strong>📸 Photo Quality</strong>
            <div style="font-size: 14px; color: #666;">Lower quality saves bandwidth</div>
        </div>
        <select style="padding: 8px; border: 1px solid #ddd; border-radius: 4px; background: white;">
            <option value="low">Low (Fast)</option>
            <option value="medium" selected>Medium</option>
            <option value="high">High (Slow)</option>
        </select>
    </div>

    <!-- Notifications -->
    <div style="display: flex; justify-content: space-between; align-items: center; padding: 15px 0;">
        <div>
            <strong>🔔 Notifications</strong>
            <div style="font-size: 14px; color: #666;">Task reminders and updates</div>
        </div>
        <label style="position: relative; display: inline-block; width: 50px; height: 24px;">
            <input type="checkbox" id="notifications" checked style="opacity: 0; width: 0; height: 0;">
            <span style="position: absolute; cursor: pointer; top: 0; left: 0; right: 0; bottom: 0; background-color: #28a745; transition: .4s; border-radius: 24px;"></span>
            <span style="position: absolute; content: ''; height: 18px; width: 18px; left: 3px; bottom: 3px; background-color: white; transition: .4s; border-radius: 50%; transform: translateX(26px);"></span>
        </label>
    </div>
</div>

<!-- Storage Info -->
<div class="card">
    <h2 class="card-title">💾 Storage Information</h2>
    <div style="background: #f8f9fa; border-radius: 6px; padding: 15px;">
        <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
            <span>Local Data:</span>
            <strong>2.3 MB</strong>
        </div>
        <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
            <span>Cached Photos:</span>
            <strong>5.7 MB</strong>
        </div>
        <div style="display: flex; justify-content: space-between; margin-bottom: 15px;">
            <span>Total Used:</span>
            <strong>8.0 MB</strong>
        </div>
        <button class="btn btn-secondary btn-block" onclick="clearCache()">
            🗑️ Clear Cache
        </button>
    </div>
</div>

<!-- App Information -->
<div class="card">
    <h2 class="card-title">ℹ️ App Information</h2>
    <div style="color: #666; font-size: 14px;">
        <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
            <span>Version:</span>
            <strong>1.0.0</strong>
        </div>
        <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
            <span>Last Updated:</span>
            <strong><?= date('M j, Y') ?></strong>
        </div>
        <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
            <span>Server Status:</span>
            <strong style="color: #28a745;">Online</strong>
        </div>
        <div style="display: flex; justify-content: space-between;">
            <span>Last Sync:</span>
            <strong><?= date('H:i') ?></strong>
        </div>
    </div>
</div>

<!-- Back Button -->
<div style="text-align: center; margin-top: 30px;">
    <a href="<?= base_url('field/dashboard') ?>" class="btn btn-secondary" style="min-width: 120px;">
        ← Back to Dashboard
    </a>
</div>

<style>
/* Toggle switch styles */
input[type="checkbox"]:checked + span {
    background-color: #28a745;
}

input[type="checkbox"]:checked + span + span {
    transform: translateX(26px);
}

input[type="checkbox"]:not(:checked) + span {
    background-color: #ccc;
}

input[type="checkbox"]:not(:checked) + span + span {
    transform: translateX(0);
}

/* Form validation styles */
.form-control:invalid {
    border-color: #dc3545;
}

.form-control:valid {
    border-color: #28a745;
}

/* Password strength indicator */
.password-strength {
    height: 4px;
    border-radius: 2px;
    margin-top: 5px;
    transition: all 0.3s;
}

.password-weak { background: #dc3545; width: 33%; }
.password-medium { background: #ffc107; width: 66%; }
.password-strong { background: #28a745; width: 100%; }
</style>

<script>
// Profile form submission
document.getElementById('profileForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const form = this;
    const submitBtn = form.querySelector('button[type="submit"]');
    const originalText = submitBtn.textContent;
    
    submitBtn.textContent = '💾 Updating...';
    submitBtn.disabled = true;
    
    // Simulate API call
    setTimeout(() => {
        alert('✅ Profile updated successfully!');
        submitBtn.textContent = originalText;
        submitBtn.disabled = false;
    }, 2000);
});

// Password form submission
document.getElementById('passwordForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const newPassword = document.getElementById('new_password').value;
    const confirmPassword = document.getElementById('confirm_password').value;
    
    if (newPassword !== confirmPassword) {
        alert('❌ Passwords do not match!\n\nPlease make sure both password fields are identical.');
        return;
    }
    
    const form = this;
    const submitBtn = form.querySelector('button[type="submit"]');
    const originalText = submitBtn.textContent;
    
    submitBtn.textContent = '🔒 Changing...';
    submitBtn.disabled = true;
    
    // Simulate API call
    setTimeout(() => {
        alert('✅ Password changed successfully!\n\nPlease use your new password for future logins.');
        form.reset();
        submitBtn.textContent = originalText;
        submitBtn.disabled = false;
    }, 2000);
});

// Password strength indicator
document.getElementById('new_password').addEventListener('input', function() {
    const password = this.value;
    let strength = 0;
    
    if (password.length >= 6) strength++;
    if (password.match(/[a-z]/) && password.match(/[A-Z]/)) strength++;
    if (password.match(/[0-9]/)) strength++;
    if (password.match(/[^a-zA-Z0-9]/)) strength++;
    
    // Remove existing strength indicator
    const existing = this.parentNode.querySelector('.password-strength');
    if (existing) existing.remove();
    
    if (password.length > 0) {
        const indicator = document.createElement('div');
        indicator.className = 'password-strength';
        
        if (strength <= 1) {
            indicator.classList.add('password-weak');
        } else if (strength <= 2) {
            indicator.classList.add('password-medium');
        } else {
            indicator.classList.add('password-strong');
        }
        
        this.parentNode.appendChild(indicator);
    }
});

// Settings toggle handlers
document.querySelectorAll('input[type="checkbox"]').forEach(checkbox => {
    checkbox.addEventListener('change', function() {
        const setting = this.id;
        const enabled = this.checked;
        
        // Save setting to localStorage
        localStorage.setItem('field_setting_' + setting, enabled);
        
        // Show feedback
        const settingName = this.parentNode.previousElementSibling.querySelector('strong').textContent;
        console.log(`${settingName}: ${enabled ? 'Enabled' : 'Disabled'}`);
    });
});

// Load saved settings
document.addEventListener('DOMContentLoaded', function() {
    document.querySelectorAll('input[type="checkbox"]').forEach(checkbox => {
        const setting = checkbox.id;
        const saved = localStorage.getItem('field_setting_' + setting);
        
        if (saved !== null) {
            checkbox.checked = saved === 'true';
        }
    });
});

function clearCache() {
    if (confirm('🗑️ Clear all cached data?\n\nThis will remove:\n• Cached photos\n• Offline data\n• App preferences\n\nYou may need to re-download some data.')) {
        // Simulate cache clearing
        const btn = event.target;
        const originalText = btn.textContent;
        
        btn.textContent = '🗑️ Clearing...';
        btn.disabled = true;
        
        setTimeout(() => {
            // Clear localStorage (except settings)
            const keys = Object.keys(localStorage);
            keys.forEach(key => {
                if (!key.startsWith('field_setting_')) {
                    localStorage.removeItem(key);
                }
            });
            
            alert('✅ Cache cleared successfully!\n\nFreed up 8.0 MB of storage space.');
            
            // Update storage display
            document.querySelector('.card:nth-last-child(3) strong:nth-of-type(1)').textContent = '0.1 MB';
            document.querySelector('.card:nth-last-child(3) strong:nth-of-type(2)').textContent = '0.0 MB';
            document.querySelector('.card:nth-last-child(3) strong:nth-of-type(3)').textContent = '0.1 MB';
            
            btn.textContent = originalText;
            btn.disabled = false;
        }, 2000);
    }
}
</script>

<?= $this->endSection() ?>
