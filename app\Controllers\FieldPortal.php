<?php

namespace App\Controllers;

use App\Models\UserModel;

class FieldPortal extends BaseController
{
    protected $userModel;

    public function __construct()
    {
        $this->userModel = new UserModel();
    }

    /**
     * Display field portal dashboard
     */
    public function index()
    {
        // Check if already logged in
        if (session()->get('field_logged_in')) {
            return redirect()->to('field/dashboard');
        }

        // If coming from admin login, check if user should be redirected to field portal
        if (session()->get('admin_logged_in')) {
            $isAdmin = session()->get('admin_is_admin');
            $isSupervisor = session()->get('admin_is_supervisor');
            
            // If user is not admin or supervisor, redirect to field portal
            if (!$isAdmin && !$isSupervisor) {
                // Transfer session data to field portal
                $sessionData = [
                    'field_user_id' => session()->get('admin_user_id'),
                    'field_email' => session()->get('admin_email'),
                    'field_name' => session()->get('admin_name'),
                    'field_role' => session()->get('admin_role'),
                    'field_org_id' => session()->get('admin_org_id'),
                    'field_logged_in' => true
                ];

                // Clear admin session
                $adminSessionKeys = [
                    'admin_user_id', 'admin_email', 'admin_name', 'admin_role',
                    'admin_is_admin', 'admin_is_supervisor', 'admin_org_id', 'admin_logged_in'
                ];
                foreach ($adminSessionKeys as $key) {
                    session()->remove($key);
                }

                // Set field session
                session()->set($sessionData);
                
                return redirect()->to('field/dashboard');
            }
        }

        return redirect()->to('admin');
    }

    /**
     * Display field dashboard
     */
    public function dashboard()
    {
        // Check if logged in
        if (!session()->get('field_logged_in')) {
            return redirect()->to('admin')->with('error', 'Please login to access the field portal.');
        }

        $data = [
            'title' => 'Field Dashboard',
            'user' => [
                'id' => session()->get('field_user_id'),
                'name' => session()->get('field_name'),
                'email' => session()->get('field_email'),
                'role' => session()->get('field_role')
            ]
        ];

        return view('field_portal/field_dashboard', $data);
    }

    /**
     * Display assigned tasks
     */
    public function tasks()
    {
        // Check if logged in
        if (!session()->get('field_logged_in')) {
            return redirect()->to('admin')->with('error', 'Please login to access the field portal.');
        }

        $data = [
            'title' => 'My Tasks',
            'user' => [
                'id' => session()->get('field_user_id'),
                'name' => session()->get('field_name'),
                'email' => session()->get('field_email'),
                'role' => session()->get('field_role')
            ]
        ];

        return view('field_portal/field_tasks', $data);
    }

    /**
     * Display price collection form
     */
    public function collect()
    {
        // Check if logged in
        if (!session()->get('field_logged_in')) {
            return redirect()->to('admin')->with('error', 'Please login to access the field portal.');
        }

        $data = [
            'title' => 'Collect Prices',
            'user' => [
                'id' => session()->get('field_user_id'),
                'name' => session()->get('field_name'),
                'email' => session()->get('field_email'),
                'role' => session()->get('field_role')
            ]
        ];

        return view('field_portal/field_collect', $data);
    }

    /**
     * Display reports
     */
    public function reports()
    {
        // Check if logged in
        if (!session()->get('field_logged_in')) {
            return redirect()->to('admin')->with('error', 'Please login to access the field portal.');
        }

        $data = [
            'title' => 'My Reports',
            'user' => [
                'id' => session()->get('field_user_id'),
                'name' => session()->get('field_name'),
                'email' => session()->get('field_email'),
                'role' => session()->get('field_role')
            ]
        ];

        return view('field_portal/field_reports', $data);
    }

    /**
     * Display profile
     */
    public function profile()
    {
        // Check if logged in
        if (!session()->get('field_logged_in')) {
            return redirect()->to('admin')->with('error', 'Please login to access the field portal.');
        }

        $data = [
            'title' => 'My Profile',
            'user' => [
                'id' => session()->get('field_user_id'),
                'name' => session()->get('field_name'),
                'email' => session()->get('field_email'),
                'role' => session()->get('field_role')
            ]
        ];

        return view('field_portal/field_profile', $data);
    }

    /**
     * Logout field user
     */
    public function logout()
    {
        // Remove field session data
        $fieldSessionKeys = [
            'field_user_id',
            'field_email', 
            'field_name',
            'field_role',
            'field_org_id',
            'field_logged_in'
        ];

        foreach ($fieldSessionKeys as $key) {
            session()->remove($key);
        }

        return redirect()->to('admin')->with('success', 'You have been logged out successfully.');
    }

    /**
     * API endpoint to get tasks (for mobile optimization)
     */
    public function apiTasks()
    {
        // Check if logged in
        if (!session()->get('field_logged_in')) {
            return $this->response->setJSON(['error' => 'Unauthorized'])->setStatusCode(401);
        }

        // Mock data for now - will be replaced with actual task data
        $tasks = [
            [
                'id' => 1,
                'title' => 'Price Collection - Papindo Wewak',
                'location' => 'Wewak Town',
                'status' => 'pending',
                'due_date' => date('Y-m-d', strtotime('+3 days')),
                'priority' => 'high'
            ],
            [
                'id' => 2,
                'title' => 'Price Collection - RH Hypermarket',
                'location' => 'Lae City',
                'status' => 'in_progress',
                'due_date' => date('Y-m-d', strtotime('+5 days')),
                'priority' => 'medium'
            ]
        ];

        return $this->response->setJSON(['tasks' => $tasks]);
    }

    /**
     * API endpoint to submit price data (for mobile optimization)
     */
    public function apiSubmitPrice()
    {
        // Check if logged in
        if (!session()->get('field_logged_in')) {
            return $this->response->setJSON(['error' => 'Unauthorized'])->setStatusCode(401);
        }

        // Check if request method is POST
        if (!$this->request->is('post')) {
            return $this->response->setJSON(['error' => 'Invalid request method'])->setStatusCode(405);
        }

        // Get JSON data
        $data = $this->request->getJSON(true);

        // Basic validation
        if (empty($data['task_id']) || empty($data['price']) || empty($data['goods_item'])) {
            return $this->response->setJSON(['error' => 'Missing required fields'])->setStatusCode(400);
        }

        // Mock successful submission - will be replaced with actual database operations
        $response = [
            'success' => true,
            'message' => 'Price data submitted successfully',
            'submission_id' => rand(1000, 9999),
            'timestamp' => date('Y-m-d H:i:s')
        ];

        return $this->response->setJSON($response);
    }
}
