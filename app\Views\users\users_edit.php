<?= $this->extend('templates/dakoii_admin_template') ?>

<?= $this->section('content') ?>
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card card-dark">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2 class="text-primary-custom mb-2">
                                <i class="bi bi-pencil me-2"></i>Edit User
                            </h2>
                            <p class="text-light mb-0">Update information for <?= esc($user['name']) ?></p>
                        </div>
                        <div>
                            <a href="<?= base_url('dakoii/system-users') ?>" class="btn btn-secondary">
                                <i class="bi bi-arrow-left me-2"></i>Back to List
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Validation Errors -->
    <?php if (isset($validation)): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="bi bi-exclamation-triangle me-2"></i>
            <strong>Please correct the following errors:</strong>
            <ul class="mb-0 mt-2">
                <?php foreach ($validation->getErrors() as $error): ?>
                    <li><?= esc($error) ?></li>
                <?php endforeach; ?>
            </ul>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Error Messages -->
    <?php if (session()->getFlashdata('error')): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="bi bi-exclamation-triangle me-2"></i>
            <?= session()->getFlashdata('error') ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Edit Form -->
    <div class="row">
        <div class="col-12">
            <div class="card card-dark">
                <div class="card-header">
                    <h5 class="card-title text-primary-custom mb-0">
                        <i class="bi bi-person-fill me-2"></i>User Information
                    </h5>
                </div>
                <div class="card-body">
                    <form method="post" action="<?= base_url('dakoii/system-users/' . $user['id'] . '/update') ?>" enctype="multipart/form-data">
                        <?= csrf_field() ?>
                        
                        <!-- System Number Display -->
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">System Number</label>
                                <div class="form-control-plaintext">
                                    <span class="badge bg-info fs-6"><?= esc($user['sys_no']) ?></span>
                                    <small class="text-muted ms-2">Auto-generated, cannot be changed</small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <!-- Organization Selection -->
                            <div class="col-md-6 mb-3">
                                <label for="org_id" class="form-label">Organization <span class="text-danger">*</span></label>
                                <select class="form-select" id="org_id" name="org_id" required>
                                    <option value="">Select Organization</option>
                                    <?php foreach ($organizations as $org): ?>
                                        <option value="<?= $org['id'] ?>" 
                                                <?= (old('org_id') ?? $user['org_id']) == $org['id'] ? 'selected' : '' ?>>
                                            <?= esc($org['org_name']) ?> (<?= esc($org['org_code']) ?>)
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>

                            <!-- Name -->
                            <div class="col-md-6 mb-3">
                                <label for="name" class="form-label">Full Name <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="name" name="name" 
                                       value="<?= old('name') ?? esc($user['name']) ?>" required>
                            </div>
                        </div>

                        <div class="row">
                            <!-- Email -->
                            <div class="col-md-6 mb-3">
                                <label for="email" class="form-label">Email Address <span class="text-danger">*</span></label>
                                <input type="email" class="form-control" id="email" name="email" 
                                       value="<?= old('email') ?? esc($user['email']) ?>" required>
                            </div>

                            <!-- Phone -->
                            <div class="col-md-6 mb-3">
                                <label for="phone" class="form-label">Phone Number</label>
                                <input type="text" class="form-control" id="phone" name="phone"
                                       value="<?= old('phone') ?? esc($user['phone']) ?>">
                            </div>
                        </div>



                        <div class="row">
                            <!-- Role -->
                            <div class="col-md-6 mb-3">
                                <label for="role" class="form-label">Role <span class="text-danger">*</span></label>
                                <select class="form-select" id="role" name="role" required>
                                    <option value="">Select Role</option>
                                    <option value="user" <?= (old('role') ?? $user['role']) == 'user' ? 'selected' : '' ?>>User</option>
                                    <option value="guest" <?= (old('role') ?? $user['role']) == 'guest' ? 'selected' : '' ?>>Guest</option>
                                </select>
                            </div>

                            <!-- Status -->
                            <div class="col-md-6 mb-3">
                                <label for="status" class="form-label">Status <span class="text-danger">*</span></label>
                                <select class="form-select" id="status" name="status" required>
                                    <option value="">Select Status</option>
                                    <option value="active" <?= (old('status') ?? $user['status']) == 'active' ? 'selected' : '' ?>>Active</option>
                                    <option value="inactive" <?= (old('status') ?? $user['status']) == 'inactive' ? 'selected' : '' ?>>Inactive</option>
                                    <option value="suspended" <?= (old('status') ?? $user['status']) == 'suspended' ? 'selected' : '' ?>>Suspended</option>
                                    <option value="pending" <?= (old('status') ?? $user['status']) == 'pending' ? 'selected' : '' ?>>Pending</option>
                                </select>
                            </div>
                        </div>

                        <div class="row">
                            <!-- Position -->
                            <div class="col-md-6 mb-3">
                                <label for="position" class="form-label">Position</label>
                                <input type="text" class="form-control" id="position" name="position" 
                                       value="<?= old('position') ?? esc($user['position']) ?>">
                            </div>

                            <!-- ID Photo -->
                            <div class="col-md-6 mb-3">
                                <label for="id_photo" class="form-label">ID Photo</label>
                                <input type="file" class="form-control" id="id_photo" name="id_photo"
                                       accept="image/*">
                                <div class="form-text">Upload new ID photo (optional). Accepted formats: JPG, PNG, GIF</div>
                                <?php if (!empty($user['id_photo'])): ?>
                                    <div class="mt-2">
                                        <small class="text-muted">Current: <?= esc($user['id_photo']) ?></small>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>

                        <!-- Permissions -->
                        <div class="row">
                            <div class="col-12 mb-3">
                                <label class="form-label">Permissions</label>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="is_admin" name="is_admin" value="1" 
                                                   <?= (old('is_admin') ?? $user['is_admin']) ? 'checked' : '' ?>>
                                            <label class="form-check-label" for="is_admin">
                                                <i class="bi bi-shield-check text-warning me-1"></i>Administrator
                                            </label>
                                            <div class="form-text">Full system access and management privileges</div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="is_supervisor" name="is_supervisor" value="1" 
                                                   <?= (old('is_supervisor') ?? $user['is_supervisor']) ? 'checked' : '' ?>>
                                            <label class="form-check-label" for="is_supervisor">
                                                <i class="bi bi-person-badge text-info me-1"></i>Supervisor
                                            </label>
                                            <div class="form-text">Supervisory access within organization</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Form Actions -->
                        <div class="row">
                            <div class="col-12">
                                <hr class="my-4">
                                <div class="d-flex justify-content-end gap-2">
                                    <a href="<?= base_url('dakoii/system-users') ?>" class="btn btn-secondary">
                                        <i class="bi bi-x-circle me-2"></i>Cancel
                                    </a>
                                    <button type="submit" class="btn btn-warning">
                                        <i class="bi bi-check-circle me-2"></i>Update User
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>
