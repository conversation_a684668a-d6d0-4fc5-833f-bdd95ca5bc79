<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= isset($title) ? $title . ' - ' : '' ?>PCOLLX Admin Portal</title>

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="<?= base_url('assets/system_images/favicon.ico') ?>">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #00B4D8;
            --secondary-color: #0077B6;
            --accent-color: #00D4AA;
            --light-color: #f8f9fa;
            --white: #ffffff;
            --gray-100: #f8f9fa;
            --gray-200: #e9ecef;
            --gray-300: #dee2e6;
            --gray-600: #6c757d;
            --gray-800: #343a40;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, var(--light-color) 0%, var(--gray-200) 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .forgot-container {
            width: 100%;
            max-width: 450px;
            padding: 2rem;
        }

        .forgot-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .forgot-header {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            padding: 2.5rem 2rem 2rem;
            text-align: center;
        }

        .forgot-header .logo-circle {
            width: 80px;
            height: 80px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            backdrop-filter: blur(10px);
        }

        .forgot-header h2 {
            margin: 0;
            font-weight: 600;
            font-size: 1.8rem;
        }

        .forgot-header p {
            margin: 0.5rem 0 0;
            opacity: 0.9;
            font-size: 0.95rem;
        }

        .forgot-body {
            padding: 2.5rem 2rem;
        }

        .info-box {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            border-left: 4px solid var(--primary-color);
        }

        .info-box h6 {
            color: var(--primary-color);
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        .info-box p {
            color: var(--gray-700);
            margin: 0;
            font-size: 0.9rem;
        }

        .form-floating {
            margin-bottom: 1.5rem;
        }

        .form-control {
            border: 2px solid var(--gray-300);
            border-radius: 12px;
            padding: 1rem 1rem;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(0, 180, 216, 0.25);
        }

        .form-floating > label {
            color: var(--gray-600);
            font-weight: 500;
        }

        .btn-reset {
            background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
            border: none;
            color: white;
            font-weight: 600;
            padding: 1rem;
            border-radius: 12px;
            width: 100%;
            font-size: 1.1rem;
            transition: all 0.3s ease;
        }

        .btn-reset:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 180, 216, 0.3);
            color: white;
        }

        .back-login {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .back-login:hover {
            color: var(--secondary-color);
            text-decoration: underline;
        }

        .divider {
            text-align: center;
            margin: 2rem 0;
            position: relative;
        }

        .divider::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 1px;
            background: var(--gray-300);
        }

        .divider span {
            background: white;
            padding: 0 1rem;
            color: var(--gray-600);
            font-size: 0.9rem;
        }

        .alert {
            border: none;
            border-radius: 12px;
            padding: 1rem 1.5rem;
            margin-bottom: 1.5rem;
        }

        .alert-success {
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
            color: #155724;
        }

        .alert-danger {
            background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
            color: #721c24;
        }

        .features-list {
            list-style: none;
            padding: 0;
            margin: 1rem 0;
        }

        .features-list li {
            padding: 0.5rem 0;
            color: var(--gray-700);
            font-size: 0.9rem;
        }

        .features-list li i {
            color: var(--primary-color);
            margin-right: 0.5rem;
        }

        @media (max-width: 576px) {
            .forgot-container {
                padding: 1rem;
            }
            
            .forgot-header {
                padding: 2rem 1.5rem 1.5rem;
            }
            
            .forgot-body {
                padding: 2rem 1.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="forgot-container">
        <div class="forgot-card">
            <!-- Header -->
            <div class="forgot-header">
                <div class="logo-circle">
                    <i class="bi bi-key-fill" style="font-size: 2.5rem;"></i>
                </div>
                <h2>Forgot Password</h2>
                <p>Admin Portal Password Recovery</p>
            </div>

            <!-- Body -->
            <div class="forgot-body">
                <!-- Display flash messages -->
                <?php if (session()->getFlashdata('success')): ?>
                    <div class="alert alert-success">
                        <i class="bi bi-check-circle me-2"></i>
                        <?= session()->getFlashdata('success') ?>
                    </div>
                <?php endif; ?>

                <?php if (session()->getFlashdata('error')): ?>
                    <div class="alert alert-danger">
                        <i class="bi bi-exclamation-triangle me-2"></i>
                        <?= session()->getFlashdata('error') ?>
                    </div>
                <?php endif; ?>

                <!-- Information Box -->
                <div class="info-box">
                    <h6><i class="bi bi-info-circle me-2"></i>Password Recovery Process</h6>
                    <p>Enter your registered email address below. If your account has admin or supervisor privileges, we'll send you a temporary 4-digit password.</p>
                    
                    <ul class="features-list">
                        <li><i class="bi bi-check-circle-fill"></i>Activates pending accounts automatically</li>
                        <li><i class="bi bi-check-circle-fill"></i>Generates secure temporary password</li>
                        <li><i class="bi bi-check-circle-fill"></i>Sent instantly to your email</li>
                        <li><i class="bi bi-check-circle-fill"></i>Admin and supervisor accounts only</li>
                    </ul>
                </div>

                <!-- Forgot Password Form -->
                <form action="<?= base_url('admin/forgot-password') ?>" method="post">
                    <?= csrf_field() ?>
                    
                    <div class="form-floating">
                        <input type="email" class="form-control" id="email" name="email" 
                               placeholder="<EMAIL>" value="<?= old('email') ?>" required>
                        <label for="email">
                            <i class="bi bi-envelope me-2"></i>Email Address
                        </label>
                    </div>

                    <button type="submit" class="btn btn-reset">
                        <i class="bi bi-send me-2"></i>
                        Send Temporary Password
                    </button>
                </form>

                <!-- Back to Login Link -->
                <div class="text-center mt-3">
                    <a href="<?= base_url('admin') ?>" class="back-login">
                        <i class="bi bi-arrow-left me-1"></i>
                        Back to Login
                    </a>
                </div>

                <!-- Divider -->
                <div class="divider">
                    <span>or</span>
                </div>

                <!-- Back to Home -->
                <div class="text-center">
                    <a href="<?= base_url() ?>" class="back-login">
                        <i class="bi bi-house me-2"></i>
                        Back to Home
                    </a>
                </div>
            </div>
        </div>

        <!-- Footer Info -->
        <div class="text-center mt-4">
            <p class="text-muted small mb-2">
                <i class="bi bi-shield-check me-1"></i>
                Secure password recovery for authorized personnel
            </p>
            <div class="d-flex align-items-center justify-content-center">
                <span class="text-muted small me-2">Powered by</span>
                <img src="<?= base_url('assets/system_images/dakoii-logo-icon.png') ?>" alt="Dakoii Systems" style="height: 16px;" class="me-2">
                <a href="https://www.dakoiims.com" target="_blank" class="text-decoration-none text-primary small">Dakoii Systems</a>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
