<?= $this->extend('templates/field_template') ?>

<?= $this->section('content') ?>

<!-- <PERSON> Header -->
<div class="card">
    <h1 class="card-title">📊 My Reports</h1>
    <p style="color: #666; margin-bottom: 0;">View your submitted data and collection reports</p>
</div>

<!-- Summary Stats -->
<div class="row">
    <div class="col col-6">
        <div class="card stat-card">
            <span class="stat-number">47</span>
            <div class="stat-label">Items This Week</div>
        </div>
    </div>
    <div class="col col-6">
        <div class="card stat-card">
            <span class="stat-number">3</span>
            <div class="stat-label">Tasks Completed</div>
        </div>
    </div>
</div>

<!-- Recent Submissions -->
<div class="card">
    <h2 class="card-title">📤 Recent Submissions</h2>
    
    <!-- Submission 1 -->
    <div style="border-bottom: 1px solid #eee; padding-bottom: 15px; margin-bottom: 15px;">
        <div style="display: flex; justify-content: between; align-items: flex-start; margin-bottom: 8px;">
            <h3 style="margin: 0; font-size: 16px; color: #333;">Brian Bell - Mount Hagen</h3>
            <span style="background: #28a745; color: white; font-size: 10px; padding: 4px 8px; border-radius: 12px; margin-left: auto;">APPROVED</span>
        </div>
        <div style="color: #666; font-size: 14px; margin-bottom: 10px;">
            📅 Submitted: <?= date('M j, Y H:i', strtotime('-1 day')) ?><br>
            🛍️ 18 items collected<br>
            💰 Total value: PGK 247.50
        </div>
        <button class="btn btn-secondary" onclick="viewSubmissionDetails(1)" style="font-size: 14px;">
            View Details
        </button>
    </div>

    <!-- Submission 2 -->
    <div style="border-bottom: 1px solid #eee; padding-bottom: 15px; margin-bottom: 15px;">
        <div style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 8px;">
            <h3 style="margin: 0; font-size: 16px; color: #333;">RH Hypermarket - Lae</h3>
            <span style="background: #ffc107; color: #212529; font-size: 10px; padding: 4px 8px; border-radius: 12px;">PENDING</span>
        </div>
        <div style="color: #666; font-size: 14px; margin-bottom: 10px;">
            📅 Submitted: <?= date('M j, Y H:i', strtotime('-3 hours')) ?><br>
            🛍️ 8 items collected (partial)<br>
            💰 Total value: PGK 89.25
        </div>
        <button class="btn btn-secondary" onclick="viewSubmissionDetails(2)" style="font-size: 14px;">
            View Details
        </button>
    </div>

    <!-- Submission 3 -->
    <div style="padding-bottom: 0;">
        <div style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 8px;">
            <h3 style="margin: 0; font-size: 16px; color: #333;">City Pharmacy - Port Moresby</h3>
            <span style="background: #dc3545; color: white; font-size: 10px; padding: 4px 8px; border-radius: 12px;">NEEDS REVIEW</span>
        </div>
        <div style="color: #666; font-size: 14px; margin-bottom: 10px;">
            📅 Submitted: <?= date('M j, Y H:i', strtotime('-2 days')) ?><br>
            🛍️ 12 items collected<br>
            💰 Total value: PGK 156.75<br>
            ⚠️ <span style="color: #dc3545;">Price variance detected on 2 items</span>
        </div>
        <button class="btn btn-secondary" onclick="viewSubmissionDetails(3)" style="font-size: 14px;">
            View Details
        </button>
    </div>
</div>

<!-- Weekly Summary -->
<div class="card">
    <h2 class="card-title">📈 This Week's Summary</h2>
    <div style="background: #f8f9fa; border-radius: 6px; padding: 15px;">
        <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
            <span>Tasks Completed:</span>
            <strong>3 of 5</strong>
        </div>
        <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
            <span>Items Collected:</span>
            <strong>47 items</strong>
        </div>
        <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
            <span>Locations Visited:</span>
            <strong>8 stores</strong>
        </div>
        <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
            <span>Data Quality Score:</span>
            <strong style="color: #28a745;">94%</strong>
        </div>
        <div style="display: flex; justify-content: space-between;">
            <span>On-Time Submissions:</span>
            <strong style="color: #28a745;">100%</strong>
        </div>
    </div>
</div>

<!-- Performance Chart (Simplified) -->
<div class="card">
    <h2 class="card-title">📊 Collection Trend</h2>
    <div style="background: #f8f9fa; border-radius: 6px; padding: 15px; text-align: center;">
        <div style="color: #666; margin-bottom: 15px;">Items collected per day (last 7 days)</div>
        <div style="display: flex; justify-content: space-between; align-items: end; height: 80px; margin-bottom: 10px;">
            <div style="background: #007bff; width: 12%; height: 60%; border-radius: 2px 2px 0 0;"></div>
            <div style="background: #007bff; width: 12%; height: 40%; border-radius: 2px 2px 0 0;"></div>
            <div style="background: #007bff; width: 12%; height: 80%; border-radius: 2px 2px 0 0;"></div>
            <div style="background: #007bff; width: 12%; height: 100%; border-radius: 2px 2px 0 0;"></div>
            <div style="background: #007bff; width: 12%; height: 30%; border-radius: 2px 2px 0 0;"></div>
            <div style="background: #007bff; width: 12%; height: 70%; border-radius: 2px 2px 0 0;"></div>
            <div style="background: #28a745; width: 12%; height: 90%; border-radius: 2px 2px 0 0;"></div>
        </div>
        <div style="display: flex; justify-content: space-between; font-size: 12px; color: #666;">
            <span>Mon</span>
            <span>Tue</span>
            <span>Wed</span>
            <span>Thu</span>
            <span>Fri</span>
            <span>Sat</span>
            <span style="color: #28a745; font-weight: 600;">Sun</span>
        </div>
    </div>
</div>

<!-- Export Options -->
<div class="card">
    <h2 class="card-title">📋 Export Data</h2>
    <div style="display: flex; gap: 10px; flex-wrap: wrap;">
        <button class="btn btn-secondary" onclick="exportData('week')" style="flex: 1; min-width: 120px;">
            📄 This Week
        </button>
        <button class="btn btn-secondary" onclick="exportData('month')" style="flex: 1; min-width: 120px;">
            📊 This Month
        </button>
    </div>
    <div style="font-size: 12px; color: #666; margin-top: 10px; text-align: center;">
        Export will be available when you have internet connection
    </div>
</div>

<!-- Back Button -->
<div style="text-align: center; margin-top: 30px;">
    <a href="<?= base_url('field/dashboard') ?>" class="btn btn-secondary" style="min-width: 120px;">
        ← Back to Dashboard
    </a>
</div>

<style>
.status-badge {
    font-size: 10px;
    font-weight: 600;
    padding: 4px 8px;
    border-radius: 12px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.chart-bar {
    transition: height 0.3s ease;
}

.export-offline {
    opacity: 0.5;
    pointer-events: none;
}
</style>

<script>
function viewSubmissionDetails(submissionId) {
    const submissions = {
        1: {
            title: 'Brian Bell - Mount Hagen',
            date: '<?= date('M j, Y H:i', strtotime('-1 day')) ?>',
            status: 'Approved',
            items: [
                { name: 'Rice 1kg', price: 'PGK 4.50', status: 'Approved' },
                { name: 'Cooking Oil 1L', price: 'PGK 8.75', status: 'Approved' },
                { name: 'Sugar 1kg', price: 'PGK 3.25', status: 'Approved' }
            ],
            notes: 'All items collected successfully. Good data quality.',
            supervisor_notes: 'Excellent work. All prices verified and approved.'
        },
        2: {
            title: 'RH Hypermarket - Lae',
            date: '<?= date('M j, Y H:i', strtotime('-3 hours')) ?>',
            status: 'Pending Review',
            items: [
                { name: 'Bread 500g', price: 'PGK 2.50', status: 'Pending' },
                { name: 'Milk 1L', price: 'PGK 5.25', status: 'Pending' },
                { name: 'Eggs 12pcs', price: 'PGK 7.80', status: 'Pending' }
            ],
            notes: 'Partial collection due to some items out of stock.',
            supervisor_notes: 'Under review by supervisor.'
        },
        3: {
            title: 'City Pharmacy - Port Moresby',
            date: '<?= date('M j, Y H:i', strtotime('-2 days')) ?>',
            status: 'Needs Review',
            items: [
                { name: 'Panadol 20tabs', price: 'PGK 12.50', status: 'Flagged - High variance' },
                { name: 'Vitamins', price: 'PGK 25.00', status: 'Flagged - High variance' },
                { name: 'Bandages', price: 'PGK 3.75', status: 'Approved' }
            ],
            notes: 'Some pharmaceutical items had higher than expected prices.',
            supervisor_notes: 'Please verify prices for flagged items and resubmit if necessary.'
        }
    };
    
    const submission = submissions[submissionId];
    if (submission) {
        let itemsList = submission.items.map(item => 
            `• ${item.name}: ${item.price} (${item.status})`
        ).join('\n');
        
        alert(`${submission.title}\n\nDate: ${submission.date}\nStatus: ${submission.status}\n\nItems:\n${itemsList}\n\nYour Notes: ${submission.notes}\n\nSupervisor Notes: ${submission.supervisor_notes}`);
    }
}

function exportData(period) {
    if (!navigator.onLine) {
        alert('📡 No internet connection\n\nExport functionality requires internet connection. Please try again when connected.');
        return;
    }
    
    const periodText = period === 'week' ? 'This Week' : 'This Month';
    
    // Simulate export preparation
    const btn = event.target;
    const originalText = btn.textContent;
    btn.textContent = '⏳ Preparing...';
    btn.disabled = true;
    
    setTimeout(() => {
        alert(`📋 ${periodText} Export Ready\n\nYour data export has been prepared and will be available for download.\n\nIn a real app, this would:\n• Generate CSV/PDF report\n• Include all submissions and statistics\n• Email or download the file`);
        
        btn.textContent = originalText;
        btn.disabled = false;
    }, 2000);
}

// Check online status and update export buttons
function updateExportButtons() {
    const exportButtons = document.querySelectorAll('button[onclick^="exportData"]');
    exportButtons.forEach(btn => {
        if (navigator.onLine) {
            btn.classList.remove('export-offline');
        } else {
            btn.classList.add('export-offline');
        }
    });
}

// Update export buttons on connection change
window.addEventListener('online', updateExportButtons);
window.addEventListener('offline', updateExportButtons);

// Initial check
document.addEventListener('DOMContentLoaded', updateExportButtons);
</script>

<?= $this->endSection() ?>
