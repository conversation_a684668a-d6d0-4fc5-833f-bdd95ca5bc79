<?= $this->extend('templates/field_template') ?>

<?= $this->section('content') ?>

<!-- Page Header -->
<div class="card">
    <h1 class="card-title">📋 My Tasks</h1>
    <p style="color: #666; margin-bottom: 0;">Your assigned price collection tasks</p>
</div>

<!-- Task Filters -->
<div class="row">
    <div class="col col-6">
        <button class="btn btn-block" onclick="filterTasks('all')" id="filter-all">
            All Tasks
        </button>
    </div>
    <div class="col col-6">
        <button class="btn btn-secondary btn-block" onclick="filterTasks('pending')" id="filter-pending">
            Pending Only
        </button>
    </div>
</div>

<!-- Tasks List -->
<div id="tasks-container">
    <!-- High Priority Task -->
    <div class="card task-card" data-status="pending" data-priority="high">
        <div style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 10px;">
            <h3 style="margin: 0; font-size: 16px; color: #333;">Price Collection - Papindo Wewak</h3>
            <span class="priority-badge high">HIGH</span>
        </div>
        <div style="color: #666; font-size: 14px; margin-bottom: 15px;">
            📍 Wewak Town Market<br>
            📅 Due: <?= date('M j, Y', strtotime('+3 days')) ?><br>
            🛍️ 15 items to collect
        </div>
        <div style="display: flex; gap: 10px;">
            <a href="<?= base_url('field/collect?task=1') ?>" class="btn" style="flex: 1; font-size: 14px;">
                Start Collection
            </a>
            <button class="btn btn-secondary" onclick="viewTaskDetails(1)" style="flex: 0 0 auto;">
                Details
            </button>
        </div>
    </div>

    <!-- Medium Priority Task -->
    <div class="card task-card" data-status="in_progress" data-priority="medium">
        <div style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 10px;">
            <h3 style="margin: 0; font-size: 16px; color: #333;">Price Collection - RH Hypermarket</h3>
            <span class="priority-badge medium">MEDIUM</span>
        </div>
        <div style="color: #666; font-size: 14px; margin-bottom: 15px;">
            📍 Lae City Center<br>
            📅 Due: <?= date('M j, Y', strtotime('+5 days')) ?><br>
            🛍️ 8 of 20 items collected
        </div>
        <div style="display: flex; gap: 10px;">
            <a href="<?= base_url('field/collect?task=2') ?>" class="btn btn-success" style="flex: 1; font-size: 14px;">
                Continue (40%)
            </a>
            <button class="btn btn-secondary" onclick="viewTaskDetails(2)" style="flex: 0 0 auto;">
                Details
            </button>
        </div>
    </div>

    <!-- Low Priority Task -->
    <div class="card task-card" data-status="pending" data-priority="low">
        <div style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 10px;">
            <h3 style="margin: 0; font-size: 16px; color: #333;">Price Collection - Stop & Shop</h3>
            <span class="priority-badge low">LOW</span>
        </div>
        <div style="color: #666; font-size: 14px; margin-bottom: 15px;">
            📍 Port Moresby<br>
            📅 Due: <?= date('M j, Y', strtotime('+7 days')) ?><br>
            🛍️ 12 items to collect
        </div>
        <div style="display: flex; gap: 10px;">
            <a href="<?= base_url('field/collect?task=3') ?>" class="btn" style="flex: 1; font-size: 14px;">
                Start Collection
            </a>
            <button class="btn btn-secondary" onclick="viewTaskDetails(3)" style="flex: 0 0 auto;">
                Details
            </button>
        </div>
    </div>

    <!-- Completed Task -->
    <div class="card task-card" data-status="completed" data-priority="medium" style="opacity: 0.7;">
        <div style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 10px;">
            <h3 style="margin: 0; font-size: 16px; color: #333;">✅ Price Collection - Brian Bell</h3>
            <span class="priority-badge completed">DONE</span>
        </div>
        <div style="color: #666; font-size: 14px; margin-bottom: 15px;">
            📍 Mount Hagen<br>
            📅 Completed: <?= date('M j, Y', strtotime('-1 day')) ?><br>
            🛍️ 18 items collected
        </div>
        <div style="display: flex; gap: 10px;">
            <button class="btn btn-secondary" style="flex: 1; font-size: 14px;" disabled>
                Completed
            </button>
            <button class="btn btn-secondary" onclick="viewTaskDetails(4)" style="flex: 0 0 auto;">
                View
            </button>
        </div>
    </div>
</div>

<!-- Back Button -->
<div style="text-align: center; margin-top: 30px;">
    <a href="<?= base_url('field/dashboard') ?>" class="btn btn-secondary" style="min-width: 120px;">
        ← Back to Dashboard
    </a>
</div>

<style>
.priority-badge {
    font-size: 10px;
    font-weight: 600;
    padding: 4px 8px;
    border-radius: 12px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.priority-badge.high {
    background: #dc3545;
    color: white;
}

.priority-badge.medium {
    background: #ffc107;
    color: #212529;
}

.priority-badge.low {
    background: #28a745;
    color: white;
}

.priority-badge.completed {
    background: #6c757d;
    color: white;
}

.task-card {
    transition: transform 0.2s, box-shadow 0.2s;
}

.task-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.filter-active {
    background: #007bff !important;
    color: white !important;
}
</style>

<script>
function filterTasks(filter) {
    const tasks = document.querySelectorAll('.task-card');
    const buttons = document.querySelectorAll('[id^="filter-"]');
    
    // Reset button styles
    buttons.forEach(btn => {
        btn.classList.remove('filter-active');
        btn.classList.add('btn-secondary');
    });
    
    // Activate current filter button
    const activeBtn = document.getElementById('filter-' + filter);
    activeBtn.classList.add('filter-active');
    activeBtn.classList.remove('btn-secondary');
    
    // Filter tasks
    tasks.forEach(task => {
        if (filter === 'all') {
            task.style.display = 'block';
        } else if (filter === 'pending') {
            const status = task.getAttribute('data-status');
            task.style.display = (status === 'pending' || status === 'in_progress') ? 'block' : 'none';
        }
    });
}

function viewTaskDetails(taskId) {
    // Simple alert for now - would be replaced with modal or detail page
    const taskDetails = {
        1: {
            title: 'Price Collection - Papindo Wewak',
            location: 'Wewak Town Market',
            items: ['Rice 1kg', 'Cooking Oil 1L', 'Sugar 1kg', 'Flour 1kg', 'Salt 1kg'],
            notes: 'Focus on staple food items. Check for any promotional prices.'
        },
        2: {
            title: 'Price Collection - RH Hypermarket',
            location: 'Lae City Center',
            items: ['Bread 500g', 'Milk 1L', 'Eggs 12pcs', 'Chicken 1kg', 'Fish 1kg'],
            notes: 'Compare prices with previous week. Note any stock shortages.'
        },
        3: {
            title: 'Price Collection - Stop & Shop',
            location: 'Port Moresby',
            items: ['Beef 1kg', 'Pork 1kg', 'Vegetables', 'Fruits', 'Canned goods'],
            notes: 'Weekend collection preferred. Check meat quality grades.'
        },
        4: {
            title: 'Price Collection - Brian Bell',
            location: 'Mount Hagen',
            items: ['Completed all 18 items'],
            notes: 'Task completed successfully. All data submitted.'
        }
    };
    
    const task = taskDetails[taskId];
    if (task) {
        alert(`${task.title}\n\nLocation: ${task.location}\nItems: ${task.items.join(', ')}\n\nNotes: ${task.notes}`);
    }
}

// Initialize with all tasks shown
document.addEventListener('DOMContentLoaded', function() {
    filterTasks('all');
});
</script>

<?= $this->endSection() ?>
