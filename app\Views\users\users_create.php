<?= $this->extend('templates/dakoii_admin_template') ?>

<?= $this->section('content') ?>
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card card-dark">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2 class="text-primary-custom mb-2">
                                <i class="bi bi-person-plus me-2"></i>Add New User
                            </h2>
                            <p class="text-light mb-0">Create a new user account in the system</p>
                        </div>
                        <div>
                            <a href="<?= base_url('dakoii/system-users') ?>" class="btn btn-secondary">
                                <i class="bi bi-arrow-left me-2"></i>Back to List
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Validation Errors -->
    <?php if (isset($validation)): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="bi bi-exclamation-triangle me-2"></i>
            <strong>Please correct the following errors:</strong>
            <ul class="mb-0 mt-2">
                <?php foreach ($validation->getErrors() as $error): ?>
                    <li><?= esc($error) ?></li>
                <?php endforeach; ?>
            </ul>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Error Messages -->
    <?php if (session()->getFlashdata('error')): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="bi bi-exclamation-triangle me-2"></i>
            <?= session()->getFlashdata('error') ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Create Form -->
    <div class="row">
        <div class="col-12">
            <div class="card card-dark">
                <div class="card-header">
                    <h5 class="card-title text-primary-custom mb-0">
                        <i class="bi bi-person-fill me-2"></i>User Information
                    </h5>
                </div>
                <div class="card-body">
                    <form method="post" action="<?= base_url('dakoii/system-users/create') ?>" enctype="multipart/form-data">
                        <?= csrf_field() ?>
                        
                        <div class="row">
                            <!-- Organization Selection -->
                            <div class="col-md-6 mb-3">
                                <label for="org_id" class="form-label">Organization <span class="text-danger">*</span></label>
                                <select class="form-select" id="org_id" name="org_id" required>
                                    <option value="">Select Organization</option>
                                    <?php foreach ($organizations as $org): ?>
                                        <option value="<?= $org['id'] ?>" <?= old('org_id') == $org['id'] ? 'selected' : '' ?>>
                                            <?= esc($org['org_name']) ?> (<?= esc($org['org_code']) ?>)
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>

                            <!-- Name -->
                            <div class="col-md-6 mb-3">
                                <label for="name" class="form-label">Full Name <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="name" name="name" 
                                       value="<?= old('name') ?>" required>
                            </div>
                        </div>

                        <div class="row">
                            <!-- Email -->
                            <div class="col-md-6 mb-3">
                                <label for="email" class="form-label">Email Address <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <input type="email" class="form-control" id="email" name="email"
                                           value="<?= old('email') ?>" required>
                                    <span class="input-group-text" id="email-status">
                                        <i class="bi bi-envelope" id="email-icon"></i>
                                    </span>
                                </div>
                                <div id="email-feedback" class="form-text"></div>
                            </div>

                            <!-- Phone -->
                            <div class="col-md-6 mb-3">
                                <label for="phone" class="form-label">Phone Number</label>
                                <input type="text" class="form-control" id="phone" name="phone"
                                       value="<?= old('phone') ?>">
                            </div>
                        </div>

                        <!-- Password Info -->
                        <div class="row">
                            <div class="col-12 mb-3">
                                <div class="alert alert-primary border-primary" style="background-color: #e7f3ff; color: #004085;">
                                    <i class="bi bi-info-circle me-2"></i>
                                    <strong>Password:</strong> A temporary 4-digit password will be automatically generated and sent to the user's email after account activation.
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <!-- Role -->
                            <div class="col-md-6 mb-3">
                                <label for="role" class="form-label">Role <span class="text-danger">*</span></label>
                                <select class="form-select" id="role" name="role" required>
                                    <option value="">Select Role</option>
                                    <option value="user" <?= old('role') == 'user' ? 'selected' : '' ?>>User</option>
                                    <option value="guest" <?= old('role') == 'guest' ? 'selected' : '' ?>>Guest</option>
                                </select>
                            </div>

                            <!-- Status Info -->
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Account Status</label>
                                <div class="form-control-plaintext">
                                    <span class="badge bg-warning">Pending Activation</span>
                                    <small class="text-muted d-block">User will receive activation email</small>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <!-- Position -->
                            <div class="col-md-6 mb-3">
                                <label for="position" class="form-label">Position</label>
                                <input type="text" class="form-control" id="position" name="position" 
                                       value="<?= old('position') ?>">
                            </div>

                            <!-- ID Photo -->
                            <div class="col-md-6 mb-3">
                                <label for="id_photo" class="form-label">ID Photo</label>
                                <input type="file" class="form-control" id="id_photo" name="id_photo"
                                       accept="image/*">
                                <div class="form-text">Upload user's ID photo (optional). Accepted formats: JPG, PNG, GIF</div>
                            </div>
                        </div>

                        <!-- Permissions -->
                        <div class="row">
                            <div class="col-12 mb-3">
                                <label class="form-label">Permissions</label>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="is_admin" name="is_admin" value="1" 
                                                   <?= old('is_admin') ? 'checked' : '' ?>>
                                            <label class="form-check-label" for="is_admin">
                                                <i class="bi bi-shield-check text-warning me-1"></i>Administrator
                                            </label>
                                            <div class="form-text">Full system access and management privileges</div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="is_supervisor" name="is_supervisor" value="1" 
                                                   <?= old('is_supervisor') ? 'checked' : '' ?>>
                                            <label class="form-check-label" for="is_supervisor">
                                                <i class="bi bi-person-badge text-info me-1"></i>Supervisor
                                            </label>
                                            <div class="form-text">Supervisory access within organization</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Form Actions -->
                        <div class="row">
                            <div class="col-12">
                                <hr class="my-4">
                                <div class="d-flex justify-content-end gap-2">
                                    <a href="<?= base_url('dakoii/system-users') ?>" class="btn btn-secondary">
                                        <i class="bi bi-x-circle me-2"></i>Cancel
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="bi bi-check-circle me-2"></i>Create User
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const emailInput = document.getElementById('email');
    const emailIcon = document.getElementById('email-icon');
    const emailStatus = document.getElementById('email-status');
    const emailFeedback = document.getElementById('email-feedback');
    const submitBtn = document.querySelector('button[type="submit"]');

    let emailCheckTimeout;
    let isEmailValid = false;

    emailInput.addEventListener('input', function() {
        const email = this.value.trim();

        // Clear previous timeout
        clearTimeout(emailCheckTimeout);

        // Reset status
        resetEmailStatus();

        // Check if email is valid format first
        if (email === '') {
            return;
        }

        if (!isValidEmail(email)) {
            showEmailStatus('invalid', 'Please enter a valid email address', 'text-danger');
            return;
        }

        // Show checking status
        showEmailStatus('checking', 'Checking availability...', 'text-info');

        // Debounce the AJAX call
        emailCheckTimeout = setTimeout(() => {
            checkEmailAvailability(email);
        }, 500);
    });

    function checkEmailAvailability(email) {
        fetch('<?= base_url('dakoii/check-email') ?>', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify({ email: email })
        })
        .then(response => response.json())
        .then(data => {
            if (data.available) {
                showEmailStatus('available', 'Email is available', 'text-success');
                isEmailValid = true;
            } else {
                showEmailStatus('taken', 'Email is already taken', 'text-danger');
                isEmailValid = false;
            }
            updateSubmitButton();
        })
        .catch(error => {
            console.error('Error checking email:', error);
            showEmailStatus('error', 'Error checking email availability', 'text-warning');
            isEmailValid = false;
            updateSubmitButton();
        });
    }

    function showEmailStatus(status, message, textClass) {
        const icons = {
            checking: 'bi-hourglass-split',
            available: 'bi-check-circle-fill text-success',
            taken: 'bi-x-circle-fill text-danger',
            invalid: 'bi-exclamation-triangle-fill text-warning',
            error: 'bi-exclamation-triangle-fill text-warning'
        };

        emailIcon.className = `bi ${icons[status]}`;
        emailFeedback.textContent = message;
        emailFeedback.className = `form-text ${textClass}`;
    }

    function resetEmailStatus() {
        emailIcon.className = 'bi bi-envelope';
        emailFeedback.textContent = '';
        emailFeedback.className = 'form-text';
        isEmailValid = false;
        updateSubmitButton();
    }

    function isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    function updateSubmitButton() {
        const email = emailInput.value.trim();
        if (email === '' || isEmailValid) {
            submitBtn.disabled = false;
        } else {
            submitBtn.disabled = true;
        }
    }
});
</script>

<?= $this->endSection() ?>
