<?= $this->extend('templates/public_template') ?>

<?= $this->section('title') ?>Account Activation<?= $this->endSection() ?>

<?= $this->section('styles') ?>
<style>
    body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
    }
    .activation-card {
        backdrop-filter: blur(10px);
        background: rgba(255, 255, 255, 0.95);
    }
    .success-icon {
        animation: bounce 2s infinite;
    }
    .error-icon {
        animation: shake 0.5s ease-in-out;
    }
    @keyframes bounce {
        0%, 20%, 50%, 80%, 100% {
            transform: translateY(0);
        }
        40% {
            transform: translateY(-10px);
        }
        60% {
            transform: translateY(-5px);
        }
    }
    @keyframes shake {
        0%, 100% { transform: translateX(0); }
        25% { transform: translateX(-5px); }
        75% { transform: translateX(5px); }
    }
</style>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="container" style="padding-top: 120px;">
    <div class="row justify-content-center" style="min-height: calc(100vh - 120px); align-items: center;">
        <div class="col-md-8 col-lg-6">
            <div class="card shadow-lg border-0 activation-card" style="border-radius: 15px;">
                <div class="card-body text-center p-5">
                    <?php if ($success): ?>
                        <div class="mb-4">
                            <i class="bi bi-check-circle-fill text-success success-icon" style="font-size: 4rem;"></i>
                        </div>
                        <h2 class="text-success mb-3">Account Activated!</h2>
                        <div class="alert alert-success" role="alert">
                            <i class="bi bi-info-circle me-2"></i>
                            <?= esc($message) ?>
                        </div>
                        <p class="text-muted mb-4">
                            You can now log in to your account using the temporary password sent to your email.
                        </p>
                        <div class="d-grid gap-2 d-md-flex justify-content-md-center">
                            <a href="<?= base_url('dakoii/login') ?>" class="btn btn-primary btn-lg">
                                <i class="bi bi-box-arrow-in-right me-2"></i>Go to Login
                            </a>
                        </div>
                    <?php else: ?>
                        <div class="mb-4">
                            <i class="bi bi-x-circle-fill text-danger error-icon" style="font-size: 4rem;"></i>
                        </div>
                        <h2 class="text-danger mb-3">Activation Failed</h2>
                        <div class="alert alert-danger" role="alert">
                            <i class="bi bi-exclamation-triangle me-2"></i>
                            <?= esc($message) ?>
                        </div>
                        <p class="text-muted mb-4">
                            Please contact the system administrator if you continue to experience issues.
                        </p>
                        <div class="d-grid gap-2 d-md-flex justify-content-md-center">
                            <a href="<?= base_url('dakoii/login') ?>" class="btn btn-outline-primary">
                                <i class="bi bi-arrow-left me-2"></i>Back to Login
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
            
            <!-- Additional Information -->
            <div class="card mt-4">
                <div class="card-body">
                    <h5 class="card-title">
                        <i class="bi bi-info-circle text-info me-2"></i>Important Information
                    </h5>
                    <ul class="list-unstyled mb-0">
                        <li class="mb-2">
                            <i class="bi bi-shield-lock text-warning me-2"></i>
                            <strong>Security:</strong> Please change your temporary password after first login
                        </li>
                        <li class="mb-2">
                            <i class="bi bi-envelope text-primary me-2"></i>
                            <strong>Email:</strong> Check your email for the temporary password
                        </li>
                        <li class="mb-0">
                            <i class="bi bi-clock text-secondary me-2"></i>
                            <strong>Support:</strong> Contact administrator if you need assistance
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>
