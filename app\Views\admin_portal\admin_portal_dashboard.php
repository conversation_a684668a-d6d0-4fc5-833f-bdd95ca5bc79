<?= $this->extend('templates/admin_template') ?>

<?= $this->section('content') ?>

<!-- Welcome Section -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body p-4">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h1 class="h3 mb-2 text-primary">
                            <i class="bi bi-speedometer2 me-3"></i>
                            Welcome to Admin Portal
                        </h1>
                        <p class="text-muted mb-0">
                            Hello <strong><?= $user['name'] ?></strong>! 
                            You are logged in as 
                            <?php if ($user['is_admin']): ?>
                                <span class="badge bg-primary">Administrator</span>
                            <?php endif; ?>
                            <?php if ($user['is_supervisor']): ?>
                                <span class="badge bg-info">Supervisor</span>
                            <?php endif; ?>
                        </p>
                        <p class="text-muted small mt-1">
                            <i class="bi bi-envelope me-1"></i><?= $user['email'] ?>
                        </p>
                    </div>
                    <div class="col-md-4 text-md-end">
                        <div class="d-flex align-items-center justify-content-md-end">
                            <div class="me-3">
                                <small class="text-muted d-block">Last Login</small>
                                <strong><?= date('M d, Y H:i') ?></strong>
                            </div>
                            <div class="stats-icon" style="background: linear-gradient(45deg, var(--primary-color), var(--secondary-color)); width: 60px; height: 60px;">
                                <i class="bi bi-person-check" style="font-size: 1.5rem;"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-4">
        <div class="stats-card">
            <div class="stats-icon" style="background: linear-gradient(45deg, #28a745, #20c997);">
                <i class="bi bi-people-fill"></i>
            </div>
            <div class="stats-number">0</div>
            <div class="stats-label">Total Users</div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-4">
        <div class="stats-card">
            <div class="stats-icon" style="background: linear-gradient(45deg, #007bff, #6610f2);">
                <i class="bi bi-clipboard-data"></i>
            </div>
            <div class="stats-number">0</div>
            <div class="stats-label">Price Collections</div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-4">
        <div class="stats-card">
            <div class="stats-icon" style="background: linear-gradient(45deg, #ffc107, #fd7e14);">
                <i class="bi bi-building"></i>
            </div>
            <div class="stats-number">0</div>
            <div class="stats-label">Organizations</div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-4">
        <div class="stats-card">
            <div class="stats-icon" style="background: linear-gradient(45deg, #dc3545, #e83e8c);">
                <i class="bi bi-graph-up"></i>
            </div>
            <div class="stats-number">0</div>
            <div class="stats-label">Reports Generated</div>
        </div>
    </div>
</div>

<!-- Main Content Area -->
<div class="row">
    <!-- Quick Actions -->
    <div class="col-lg-8 mb-4">
        <div class="card">
            <div class="card-header">
                <h5><i class="bi bi-lightning-charge me-2"></i>Quick Actions</h5>
            </div>
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-md-6">
                        <div class="d-grid">
                            <button class="btn btn-outline-custom" onclick="alert('Feature coming soon!')">
                                <i class="bi bi-person-plus me-2"></i>
                                Manage Users
                            </button>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="d-grid">
                            <button class="btn btn-outline-custom" onclick="alert('Feature coming soon!')">
                                <i class="bi bi-building me-2"></i>
                                Manage Organizations
                            </button>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="d-grid">
                            <button class="btn btn-outline-custom" onclick="alert('Feature coming soon!')">
                                <i class="bi bi-clipboard-data me-2"></i>
                                View Reports
                            </button>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="d-grid">
                            <button class="btn btn-outline-custom" onclick="alert('Feature coming soon!')">
                                <i class="bi bi-gear me-2"></i>
                                System Settings
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- System Information -->
    <div class="col-lg-4 mb-4">
        <div class="card">
            <div class="card-header">
                <h5><i class="bi bi-info-circle me-2"></i>System Information</h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <small class="text-muted d-block">System Version</small>
                    <strong>PCOLLX v1.0.0</strong>
                </div>
                <div class="mb-3">
                    <small class="text-muted d-block">Database Status</small>
                    <span class="badge bg-success">
                        <i class="bi bi-check-circle me-1"></i>Connected
                    </span>
                </div>
                <div class="mb-3">
                    <small class="text-muted d-block">Server Status</small>
                    <span class="badge bg-success">
                        <i class="bi bi-check-circle me-1"></i>Online
                    </span>
                </div>
                <div class="mb-3">
                    <small class="text-muted d-block">Last Backup</small>
                    <strong><?= date('M d, Y') ?></strong>
                </div>
                <hr>
                <div class="text-center">
                    <button class="btn btn-primary-custom btn-sm" onclick="alert('Feature coming soon!')">
                        <i class="bi bi-arrow-clockwise me-1"></i>
                        Refresh Status
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Activity -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="bi bi-clock-history me-2"></i>Recent Activity</h5>
            </div>
            <div class="card-body">
                <div class="text-center py-5">
                    <div class="mb-3">
                        <i class="bi bi-clock-history text-muted" style="font-size: 3rem;"></i>
                    </div>
                    <h6 class="text-muted">No Recent Activity</h6>
                    <p class="text-muted small">
                        System activity and user actions will appear here once the system is in use.
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    // Welcome message
    document.addEventListener('DOMContentLoaded', function() {
        // Show success message if redirected from login
        <?php if (session()->getFlashdata('success')): ?>
            // You can add a toast notification here if needed
            console.log('<?= session()->getFlashdata('success') ?>');
        <?php endif; ?>
        
        // Auto-refresh stats every 30 seconds (when implemented)
        // setInterval(refreshStats, 30000);
    });
    
    function refreshStats() {
        // This will be implemented when we have actual data to display
        console.log('Refreshing statistics...');
    }
    
    // Quick action handlers
    function manageUsers() {
        alert('User management feature will be implemented in the next phase.');
    }
    
    function manageOrganizations() {
        alert('Organization management feature will be implemented in the next phase.');
    }
    
    function viewReports() {
        alert('Reports feature will be implemented in the next phase.');
    }
    
    function systemSettings() {
        alert('System settings feature will be implemented in the next phase.');
    }
</script>
<?= $this->endSection() ?>
