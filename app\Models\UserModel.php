<?php

namespace App\Models;

use CodeIgniter\Model;

class UserModel extends Model
{
    protected $table = 'users';
    protected $primaryKey = 'id';
    protected $returnType = 'array';
    protected $useSoftDeletes = true;
    
    protected $allowedFields = [
        'org_id',
        'sys_no',
        'name',
        'password',
        'role',
        'is_admin',
        'is_supervisor',
        'position',
        'id_photo',
        'phone',
        'email',
        'status',
        'activation_token',
        'activation_sent_at',
        'activated_at',
        'created_by',
        'updated_by',
        'deleted_by'
    ];
    
    protected $useTimestamps = true;
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';
    protected $deletedField = 'deleted_at';
    
    protected $validationRules = [
        'org_id' => 'required|integer',
        'name' => 'required|max_length[255]',
        'password' => 'permit_empty|min_length[4]',
        'role' => 'required|in_list[user,guest]',
        'is_admin' => 'permit_empty|integer|in_list[0,1]',
        'is_supervisor' => 'permit_empty|integer|in_list[0,1]',
        'position' => 'permit_empty|max_length[255]',
        'id_photo' => 'permit_empty|max_length[500]',
        'phone' => 'permit_empty|max_length[200]',
        'email' => 'required|valid_email|max_length[500]|is_unique[users.email,id,{id}]',
        'status' => 'required|max_length[20]'
    ];
    
    protected $validationMessages = [
        'org_id' => [
            'required' => 'Organization is required',
            'integer' => 'Invalid organization selection'
        ],
        'name' => [
            'required' => 'Name is required',
            'max_length' => 'Name cannot exceed 255 characters'
        ],
        'password' => [
            'min_length' => 'Password must be at least 4 characters long'
        ],
        'role' => [
            'required' => 'Role is required',
            'in_list' => 'Invalid role selection. Must be user or guest'
        ],
        'is_admin' => [
            'integer' => 'Admin flag must be 0 or 1',
            'in_list' => 'Admin flag must be 0 or 1'
        ],
        'is_supervisor' => [
            'integer' => 'Supervisor flag must be 0 or 1',
            'in_list' => 'Supervisor flag must be 0 or 1'
        ],
        'email' => [
            'required' => 'Email is required',
            'valid_email' => 'Please enter a valid email address',
            'is_unique' => 'Email already exists'
        ]
    ];
    
    protected $skipValidation = false;
    
    /**
     * Hash password before saving and generate system number
     */
    protected $beforeInsert = ['hashPassword', 'generateSystemNumber'];
    protected $beforeUpdate = ['hashPassword'];
    
    protected function hashPassword(array $data)
    {
        if (isset($data['data']['password']) && !empty($data['data']['password'])) {
            $data['data']['password'] = password_hash($data['data']['password'], PASSWORD_DEFAULT);
        }

        return $data;
    }

    /**
     * Generate unique system number before insert
     */
    protected function generateSystemNumber(array $data)
    {
        if (!isset($data['data']['sys_no'])) {
            $data['data']['sys_no'] = $this->getNextSystemNumber();
        }

        return $data;
    }

    /**
     * Get next system number (year + increment)
     */
    private function getNextSystemNumber(): int
    {
        $currentYear = date('Y');

        // Get the highest sys_no for current year
        $lastUser = $this->select('sys_no')
                        ->where('sys_no >=', $currentYear . '01')
                        ->where('sys_no <', ($currentYear + 1) . '01')
                        ->orderBy('sys_no', 'DESC')
                        ->first();

        if ($lastUser) {
            // Extract the increment part and add 1
            $lastSysNo = $lastUser['sys_no'];
            $increment = intval(substr($lastSysNo, 4)) + 1;
            return intval($currentYear . str_pad($increment, 2, '0', STR_PAD_LEFT));
        } else {
            // First user of the year
            return intval($currentYear . '01');
        }
    }
    
    /**
     * Get users by organization
     */
    public function getUsersByOrg(int $orgId)
    {
        return $this->where('org_id', $orgId)
                   ->where('deleted_at', null)
                   ->findAll();
    }
    
    /**
     * Get users with organization info
     */
    public function getUsersWithOrg()
    {
        return $this->select('users.*, dakoii_org.org_name, dakoii_org.org_code')
                   ->join('dakoii_org', 'dakoii_org.id = users.org_id', 'left')
                   ->where('users.deleted_at', null)
                   ->findAll();
    }
    
    /**
     * Get active users
     */
    public function getActiveUsers()
    {
        return $this->where('status', 'active')
                   ->where('deleted_at', null)
                   ->findAll();
    }
    
    /**
     * Get user statistics
     */
    public function getUserStats()
    {
        $stats = [];
        
        // Total users
        $stats['total'] = $this->where('deleted_at', null)->countAllResults();
        
        // Active users
        $stats['active'] = $this->where('status', 'active')
                               ->where('deleted_at', null)
                               ->countAllResults();
        
        // Users by role
        $roles = ['user', 'guest'];
        foreach ($roles as $role) {
            $stats['role_' . $role] = $this->where('role', $role)
                                          ->where('deleted_at', null)
                                          ->countAllResults();
        }

        // Admin users
        $stats['admin'] = $this->where('is_admin', 1)
                              ->where('deleted_at', null)
                              ->countAllResults();

        // Supervisor users
        $stats['supervisor'] = $this->where('is_supervisor', 1)
                                   ->where('deleted_at', null)
                                   ->countAllResults();
        
        return $stats;
    }
    
    /**
     * Search users
     */
    public function searchUsers(string $keyword, int $orgId = null)
    {
        $builder = $this->groupStart()
                       ->like('name', $keyword)
                       ->orLike('email', $keyword)
                       ->orLike('phone', $keyword)
                       ->orLike('position', $keyword)
                   ->groupEnd()
                   ->where('deleted_at', null);
        
        if ($orgId) {
            $builder->where('org_id', $orgId);
        }
        
        return $builder->findAll();
    }
    
    /**
     * Verify user credentials (any user - status check done in controller)
     */
    public function verifyCredentials(string $email, string $password)
    {
        $user = $this->where('email', $email)
                    ->where('deleted_at', null)
                    ->first();

        if ($user && password_verify($password, $user['password'])) {
            return $user;
        }

        return false;
    }

    /**
     * Get admin users
     */
    public function getAdminUsers()
    {
        return $this->where('is_admin', 1)
                   ->where('deleted_at', null)
                   ->findAll();
    }

    /**
     * Get supervisor users
     */
    public function getSupervisorUsers()
    {
        return $this->where('is_supervisor', 1)
                   ->where('deleted_at', null)
                   ->findAll();
    }

    /**
     * Check if user is admin
     */
    public function isAdmin(int $userId): bool
    {
        $user = $this->find($userId);
        return $user && $user['is_admin'] == 1;
    }

    /**
     * Check if user is supervisor
     */
    public function isSupervisor(int $userId): bool
    {
        $user = $this->find($userId);
        return $user && $user['is_supervisor'] == 1;
    }

    /**
     * Get users by admin/supervisor status
     */
    public function getUsersByPermission(bool $isAdmin = null, bool $isSupervisor = null)
    {
        $builder = $this->where('deleted_at', null);

        if ($isAdmin !== null) {
            $builder->where('is_admin', $isAdmin ? 1 : 0);
        }

        if ($isSupervisor !== null) {
            $builder->where('is_supervisor', $isSupervisor ? 1 : 0);
        }

        return $builder->findAll();
    }

    /**
     * Generate activation token for user
     */
    public function generateActivationToken(int $userId): string
    {
        $token = bin2hex(random_bytes(32));

        $this->update($userId, [
            'activation_token' => $token,
            'activation_sent_at' => date('Y-m-d H:i:s'),
            'status' => 'pending'
        ]);

        return $token;
    }

    /**
     * Activate user account using token
     */
    public function activateAccount(string $token): bool
    {
        $user = $this->where('activation_token', $token)
                    ->where('status', 'pending')
                    ->first();

        if (!$user) {
            return false;
        }

        // Check if token is not older than 24 hours
        $sentAt = strtotime($user['activation_sent_at']);
        $now = time();
        $hoursDiff = ($now - $sentAt) / 3600;

        if ($hoursDiff > 24) {
            return false; // Token expired
        }

        // Activate the account
        $this->update($user['id'], [
            'activated_at' => date('Y-m-d H:i:s'),
            'activation_token' => null,
            'status' => 'active'
        ]);

        return true;
    }

    /**
     * Generate temporary 4-digit password
     */
    public function generateTemporaryPassword(int $userId): string
    {
        $tempPassword = str_pad(rand(1000, 9999), 4, '0', STR_PAD_LEFT);

        // Update user with temporary password
        $this->update($userId, [
            'password' => $tempPassword // Will be hashed by beforeUpdate callback
        ]);

        return $tempPassword;
    }

    /**
     * Check if user account is activated
     */
    public function isActivated(int $userId): bool
    {
        $user = $this->find($userId);
        return $user && $user['status'] === 'active';
    }

    /**
     * Get user by activation token
     */
    public function getUserByActivationToken(string $token)
    {
        return $this->where('activation_token', $token)
                   ->where('status', 'pending')
                   ->first();
    }

    /**
     * Verify admin/supervisor credentials for admin portal
     */
    public function verifyAdminCredentials(string $email, string $password)
    {
        // Find user by email who is admin or supervisor
        $user = $this->where('email', $email)
                    ->where('deleted_at', null)
                    ->groupStart()
                        ->where('is_admin', 1)
                        ->orWhere('is_supervisor', 1)
                    ->groupEnd()
                    ->first();

        if ($user && password_verify($password, $user['password'])) {
            return $user;
        }

        return false;
    }

    /**
     * Get admin or supervisor user by email
     */
    public function getAdminOrSupervisorByEmail(string $email)
    {
        return $this->where('email', $email)
                   ->where('deleted_at', null)
                   ->groupStart()
                       ->where('is_admin', 1)
                       ->orWhere('is_supervisor', 1)
                   ->groupEnd()
                   ->first();
    }

    /**
     * Generate temporary 4-digit password for admin portal
     */
    public function generateTemporaryPasswordForAdmin(int $userId): string
    {
        $tempPassword = str_pad(rand(1000, 9999), 4, '0', STR_PAD_LEFT);

        // Update user with temporary password
        $this->update($userId, [
            'password' => $tempPassword // Will be hashed by beforeUpdate callback
        ]);

        return $tempPassword;
    }

    /**
     * Check if user has admin portal access
     */
    public function hasAdminAccess(int $userId): bool
    {
        $user = $this->find($userId);
        return $user && ($user['is_admin'] == 1 || $user['is_supervisor'] == 1);
    }

    /**
     * Get admin portal users (admin and supervisors)
     */
    public function getAdminPortalUsers()
    {
        return $this->where('deleted_at', null)
                   ->groupStart()
                       ->where('is_admin', 1)
                       ->orWhere('is_supervisor', 1)
                   ->groupEnd()
                   ->findAll();
    }

    /**
     * Resend activation token
     */
    public function resendActivationToken(int $userId): string
    {
        return $this->generateActivationToken($userId);
    }
}
