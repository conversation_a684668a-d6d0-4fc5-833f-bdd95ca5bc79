<?= $this->extend('templates/field_template') ?>

<?= $this->section('content') ?>

<!-- Welcome Message -->
<div class="card">
    <h1 class="card-title">Welcome, <?= esc($user['name']) ?>!</h1>
    <p style="color: #666; margin-bottom: 0;">Ready to collect price data? Choose an option below to get started.</p>
</div>

<!-- Quick Stats -->
<div class="row">
    <div class="col col-6">
        <div class="card stat-card">
            <span class="stat-number">5</span>
            <div class="stat-label">Pending Tasks</div>
        </div>
    </div>
    <div class="col col-6">
        <div class="card stat-card">
            <span class="stat-number">12</span>
            <div class="stat-label">Completed Today</div>
        </div>
    </div>
</div>

<!-- Main Navigation Buttons -->
<div class="card">
    <h2 class="card-title">Quick Actions</h2>
    
    <!-- My Tasks -->
    <a href="<?= base_url('field/tasks') ?>" class="btn btn-block">
        📋 My Tasks
        <div style="font-size: 14px; opacity: 0.8; margin-top: 4px;">View assigned price collection tasks</div>
    </a>
    
    <!-- Collect Prices -->
    <a href="<?= base_url('field/collect') ?>" class="btn btn-success btn-block">
        💰 Collect Prices
        <div style="font-size: 14px; opacity: 0.8; margin-top: 4px;">Start collecting price data</div>
    </a>
    
    <!-- My Reports -->
    <a href="<?= base_url('field/reports') ?>" class="btn btn-secondary btn-block">
        📊 My Reports
        <div style="font-size: 14px; opacity: 0.8; margin-top: 4px;">View submitted data and reports</div>
    </a>
    
    <!-- Profile -->
    <a href="<?= base_url('field/profile') ?>" class="btn btn-warning btn-block">
        👤 My Profile
        <div style="font-size: 14px; opacity: 0.8; margin-top: 4px;">Update profile and settings</div>
    </a>
</div>

<!-- Recent Activity -->
<div class="card">
    <h2 class="card-title">Recent Activity</h2>
    <div style="color: #666; font-size: 14px;">
        <div style="padding: 10px 0; border-bottom: 1px solid #eee;">
            ✅ Completed: Price Collection - Papindo Wewak
            <div style="font-size: 12px; color: #999; margin-top: 2px;">2 hours ago</div>
        </div>
        <div style="padding: 10px 0; border-bottom: 1px solid #eee;">
            📝 Started: Price Collection - RH Hypermarket
            <div style="font-size: 12px; color: #999; margin-top: 2px;">4 hours ago</div>
        </div>
        <div style="padding: 10px 0;">
            📤 Submitted: Weekly Report
            <div style="font-size: 12px; color: #999; margin-top: 2px;">Yesterday</div>
        </div>
    </div>
</div>

<!-- Quick Tips -->
<div class="card" style="background: #e8f4fd; border: 1px solid #bee5eb;">
    <h2 class="card-title" style="color: #0c5460;">💡 Quick Tips</h2>
    <ul style="color: #0c5460; font-size: 14px; margin: 0; padding-left: 20px;">
        <li style="margin-bottom: 8px;">Always verify product details before recording prices</li>
        <li style="margin-bottom: 8px;">Take photos when product information is unclear</li>
        <li style="margin-bottom: 8px;">Submit data as soon as you have internet connection</li>
        <li>Contact your supervisor if you encounter any issues</li>
    </ul>
</div>

<!-- Logout Button -->
<div style="text-align: center; margin-top: 30px;">
    <a href="<?= base_url('field/logout') ?>" class="btn btn-danger" 
       onclick="return confirm('Are you sure you want to logout?')"
       style="min-width: 120px;">
        🚪 Logout
    </a>
</div>

<!-- Display success/error messages -->
<?php if (session()->getFlashdata('success')): ?>
    <div class="alert alert-success">
        <?= session()->getFlashdata('success') ?>
    </div>
<?php endif; ?>

<?php if (session()->getFlashdata('error')): ?>
    <div class="alert alert-danger">
        <?= session()->getFlashdata('error') ?>
    </div>
<?php endif; ?>

<style>
/* Additional mobile-specific styles */
.btn {
    position: relative;
    overflow: hidden;
}

.btn:active {
    transform: scale(0.98);
}

/* Improve touch targets */
.btn-block {
    min-height: 60px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
}

/* Loading animation */
@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.loading {
    animation: pulse 1.5s infinite;
}

/* Offline indicator */
.offline-indicator {
    position: fixed;
    top: 60px;
    left: 50%;
    transform: translateX(-50%);
    background: #dc3545;
    color: white;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 14px;
    z-index: 1000;
    display: none;
}

.offline .offline-indicator {
    display: block;
}
</style>

<!-- Offline indicator -->
<div class="offline-indicator">
    📡 You're offline. Data will sync when connection is restored.
</div>

<script>
// Simple offline detection
window.addEventListener('online', function() {
    document.body.classList.remove('offline');
});

window.addEventListener('offline', function() {
    document.body.classList.add('offline');
});

// Check initial connection status
if (!navigator.onLine) {
    document.body.classList.add('offline');
}

// Simple touch feedback
document.addEventListener('DOMContentLoaded', function() {
    const buttons = document.querySelectorAll('.btn');
    buttons.forEach(button => {
        button.addEventListener('touchstart', function() {
            this.style.opacity = '0.8';
        });
        
        button.addEventListener('touchend', function() {
            this.style.opacity = '1';
        });
        
        button.addEventListener('touchcancel', function() {
            this.style.opacity = '1';
        });
    });
});
</script>

<?= $this->endSection() ?>
